import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '../../../components/firebase/admin';

export async function GET(req: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(req.url);
    const requestId = url.searchParams.get('requestId');
    const limit = parseInt(url.searchParams.get('limit') || '10', 10);
    const page = parseInt(url.searchParams.get('page') || '1', 10);

    // If requestId is provided, get a specific document
    if (requestId) {
      const docRef = adminDb.collection('Agent_Output').doc(requestId);
      const doc = await docRef.get();

      if (!doc.exists) {
        return NextResponse.json(
          { error: 'Document not found' },
          { status: 404 }
        );
      }

      const data = doc.data();

      if (!data) {
        return NextResponse.json(
          { error: 'Document data not found' },
          { status: 404 }
        );
      }

      // Convert Firebase timestamps to JavaScript Date objects
      const convertTimestamp = (timestamp: any): Date | null => {
        if (!timestamp) return null;
        if (timestamp.toDate && typeof timestamp.toDate === 'function') {
          return timestamp.toDate();
        }
        if (timestamp.seconds && typeof timestamp.seconds === 'number') {
          return new Date(timestamp.seconds * 1000);
        }
        if (timestamp._seconds && typeof timestamp._seconds === 'number') {
          return new Date(timestamp._seconds * 1000);
        }
        // Try to parse as date string or number
        try {
          return new Date(timestamp);
        } catch {
          return null;
        }
      };

      return NextResponse.json({
        id: doc.id,
        ...data,
        timestamp: convertTimestamp(data.timestamp),
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt)
      });
    }

    // Otherwise, get a paginated list of documents
    // Get all documents and sort them in the application to handle missing timestamp fields
    const allDocsSnapshot = await adminDb.collection('Agent_Output').get();

    if (allDocsSnapshot.empty) {
      return NextResponse.json({
        results: [],
        hasMore: false,
        page,
        limit,
        lastTimestamp: null
      });
    }

    // Convert all documents and sort them by timestamp (with fallbacks)
    const allOutputs = allDocsSnapshot.docs.map(doc => {
      const data = doc.data();

      if (!data) {
        console.warn(`Document ${doc.id} has no data, skipping`);
        return null;
      }

      // Convert Firebase timestamps to JavaScript Date objects
      const convertTimestamp = (timestamp: any): Date | null => {
        if (!timestamp) return null;
        if (timestamp.toDate && typeof timestamp.toDate === 'function') {
          return timestamp.toDate();
        }
        if (timestamp.seconds && typeof timestamp.seconds === 'number') {
          return new Date(timestamp.seconds * 1000);
        }
        if (timestamp._seconds && typeof timestamp._seconds === 'number') {
          return new Date(timestamp._seconds * 1000);
        }
        // Try to parse as date string or number
        try {
          return new Date(timestamp);
        } catch {
          return null;
        }
      };

      const convertedData = {
        id: doc.id,
        ...data,
        timestamp: convertTimestamp(data.timestamp),
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt)
      };

      // Debug logging for BusinessAnalysis and Investigative Research outputs
      if (data.agentType === 'BusinessAnalysis' ||
          (data.agentType && data.agentType.startsWith('Investigative Research'))) {
        console.log(`[API_TIMESTAMP_DEBUG] ${data.agentType} output ${doc.id}:`, {
          originalTimestamp: data.timestamp,
          originalCreatedAt: data.createdAt,
          convertedTimestamp: convertedData.timestamp,
          convertedCreatedAt: convertedData.createdAt
        });
      }

      return convertedData;
    }).filter((item): item is NonNullable<typeof item> => Boolean(item)); // Remove null entries with type guard

    // Sort by timestamp (desc), then by createdAt (desc), then by document ID
    allOutputs.sort((a, b) => {
      // Primary sort: timestamp (most recent first)
      const aTime = a.timestamp || a.createdAt || new Date(0);
      const bTime = b.timestamp || b.createdAt || new Date(0);

      if (aTime.getTime() !== bTime.getTime()) {
        return bTime.getTime() - aTime.getTime(); // Descending
      }

      // Secondary sort: document ID (for consistency)
      return b.id.localeCompare(a.id);
    });

    // Implement pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedOutputs = allOutputs.slice(startIndex, endIndex);
    const hasMore = endIndex < allOutputs.length;

    // Get the last timestamp for pagination cursor (from the last item in current page)
    const lastItem = paginatedOutputs[paginatedOutputs.length - 1];
    const lastTimestamp = lastItem ? (lastItem.timestamp || lastItem.createdAt) : null;

    return NextResponse.json({
      results: paginatedOutputs,
      hasMore,
      page,
      limit,
      lastTimestamp: lastTimestamp ? JSON.stringify(lastTimestamp) : null
    });

      // Remove the extra item if there are more results
      const outputs = querySnapshot.docs.slice(0, limit).map(doc => {
        const data = doc.data();

        if (!data) {
          console.warn(`Document ${doc.id} has no data, skipping`);
          return null;
        }

        // Convert Firebase timestamps to JavaScript Date objects
        const convertTimestamp = (timestamp: any): Date | null => {
          if (!timestamp) return null;
          if (timestamp.toDate && typeof timestamp.toDate === 'function') {
            return timestamp.toDate();
          }
          if (timestamp.seconds && typeof timestamp.seconds === 'number') {
            return new Date(timestamp.seconds * 1000);
          }
          if (timestamp._seconds && typeof timestamp._seconds === 'number') {
            return new Date(timestamp._seconds * 1000);
          }
          // Try to parse as date string or number
          try {
            return new Date(timestamp);
          } catch {
            return null;
          }
        };

        const convertedData = {
          id: doc.id,
          ...data,
          timestamp: convertTimestamp(data.timestamp),
          createdAt: convertTimestamp(data.createdAt),
          updatedAt: convertTimestamp(data.updatedAt)
        };

        // Debug logging for BusinessAnalysis and Investigative Research outputs
        if (data.agentType === 'BusinessAnalysis' ||
            (data.agentType && data.agentType.startsWith('Investigative Research'))) {
          console.log(`[API_TIMESTAMP_DEBUG] ${data.agentType} output ${doc.id}:`, {
            originalTimestamp: data.timestamp,
            originalCreatedAt: data.createdAt,
            convertedTimestamp: convertedData.timestamp,
            convertedCreatedAt: convertedData.createdAt
          });
        }

        return convertedData;
      }).filter(Boolean); // Remove null entries

      // Store the last document as a cursor in the response
      // If there are no results or fewer than limit, lastDoc will be the last item
      const lastIndex = Math.min(querySnapshot.docs.length - 1, limit - 1);
      const lastDoc = lastIndex >= 0 ? querySnapshot.docs[lastIndex] : null;
      const lastDocData = lastDoc?.data();
      const lastTimestamp = lastDocData ? lastDocData.timestamp : null;

      return NextResponse.json({
        results: outputs,
        hasMore,
        page,
        limit,
        lastTimestamp: lastTimestamp ? JSON.stringify(lastTimestamp) : null
      });
    } else {
      // Get the lastTimestamp from the query parameters
      const lastTimestampStr = url.searchParams.get('lastTimestamp');

      if (!lastTimestampStr) {
        return NextResponse.json(
          { error: 'Missing lastTimestamp parameter for pagination' },
          { status: 400 }
        );
      }

      try {
        // Parse the lastTimestamp
        const lastTimestamp = JSON.parse(lastTimestampStr);

        // Get documents after the last timestamp
        const querySnapshot = await query
          .startAfter(lastTimestamp)
          .limit(limit + 1)
          .get();

        // If no documents were found, return empty results
        if (querySnapshot.empty) {
          return NextResponse.json({
            results: [],
            hasMore: false,
            page,
            limit,
            lastTimestamp: null
          });
        }

        // Check if there are more results
        const hasMore = querySnapshot.docs.length > limit;

        // Remove the extra item if there are more results
        const outputs = querySnapshot.docs.slice(0, limit).map(doc => {
          const data = doc.data();

          if (!data) {
            console.warn(`Document ${doc.id} has no data, skipping`);
            return null;
          }

          // Convert Firebase timestamps to JavaScript Date objects
          const convertTimestamp = (timestamp: any): Date | null => {
            if (!timestamp) return null;
            if (timestamp.toDate && typeof timestamp.toDate === 'function') {
              return timestamp.toDate();
            }
            if (timestamp.seconds && typeof timestamp.seconds === 'number') {
              return new Date(timestamp.seconds * 1000);
            }
            if (timestamp._seconds && typeof timestamp._seconds === 'number') {
              return new Date(timestamp._seconds * 1000);
            }
            // Try to parse as date string or number
            try {
              return new Date(timestamp);
            } catch {
              return null;
            }
          };

          const convertedData = {
            id: doc.id,
            ...data,
            timestamp: convertTimestamp(data.timestamp),
            createdAt: convertTimestamp(data.createdAt),
            updatedAt: convertTimestamp(data.updatedAt)
          };

          // Debug logging for BusinessAnalysis and Investigative Research outputs
          if (data.agentType === 'BusinessAnalysis' ||
              (data.agentType && data.agentType.startsWith('Investigative Research'))) {
            console.log(`[API_TIMESTAMP_DEBUG] ${data.agentType} output ${doc.id}:`, {
              originalTimestamp: data.timestamp,
              originalCreatedAt: data.createdAt,
              convertedTimestamp: convertedData.timestamp,
              convertedCreatedAt: convertedData.createdAt
            });
          }

          return convertedData;
        }).filter(Boolean); // Remove null entries

        // Store the last document as a cursor in the response
        // If there are no results, lastDoc will be null
        const lastIndex = querySnapshot.docs.length - 1;
        const lastDoc = lastIndex >= 0 ? querySnapshot.docs[lastIndex] : null;
        const lastDocData = lastDoc?.data();
        const newLastTimestamp = lastDocData ? lastDocData.timestamp : null;

        return NextResponse.json({
          results: outputs,
          hasMore,
          page,
          limit,
          lastTimestamp: newLastTimestamp ? JSON.stringify(newLastTimestamp) : null
        });
      } catch (error) {
        console.error('Error parsing lastTimestamp:', error);
        return NextResponse.json(
          { error: 'Invalid lastTimestamp parameter' },
          { status: 400 }
        );
      }
    }
  } catch (error) {
    console.error('Error retrieving agent outputs:', error);

    return NextResponse.json(
      {
        error: 'Failed to retrieve agent outputs',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
