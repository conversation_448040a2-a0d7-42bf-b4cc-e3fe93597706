/**
 * Investigative Research Agent Constants
 * 
 * Client-safe constants that can be imported in both server and client environments
 * without triggering Node.js module resolution issues.
 */

// Investigation Types Enum
export enum InvestigationType {
  FINANCIAL = 'financial',
  POLITICAL = 'political', 
  TECHNOLOGY = 'technology',
  SOCIAL_AFFAIRS = 'social_affairs',
  CORPORATE = 'corporate',
  ENVIRONMENTAL = 'environmental',
  INVESTIGATIVE = 'investigative',
  FEATURE = 'feature'
}

// Default Journalist Models (using correct provider-model combinations)
export const DEFAULT_JOURNALIST_MODELS = {
  INVESTIGATIVE: 'claude-sonnet-4-0',  // Anthropic
  FINANCIAL: 'gpt-4o',                 // OpenAI
  POLITICAL: 'gemini-2.5-pro',         // Google
  TECHNOLOGY: 'gpt-4o',                // OpenAI (changed from o3 to avoid issues)
  SOCIAL_AFFAIRS: 'claude-sonnet-4-0', // Anthropic
  FEATURE: 'claude-sonnet-4-0'         // Anthropic (changed from opus to sonnet for availability)
};

// Default System Models (using correct provider-model combinations)
export const DEFAULT_SYSTEM_MODELS = {
  CRITERIA: 'gemini-2.5-pro',          // Google
  OPTIMIZATION: 'gpt-4o',              // OpenAI (changed from o3 to avoid issues)
  ASSESSMENT: 'claude-sonnet-4-0',     // Anthropic
  CONSOLIDATION: 'gpt-4o'              // OpenAI (changed from o3 to avoid issues)
};

// Comparison Models (using stable, available models)
export const DEFAULT_COMPARISON_MODELS = [
  'gpt-4o',                    // OpenAI - stable and reliable
  'claude-sonnet-4-0',         // Anthropic - high quality reasoning
  'gemini-2.5-pro'             // Google - good for analysis
];

export const AVAILABLE_COMPARISON_MODELS = [
  'gpt-4o',                    // OpenAI
  'claude-sonnet-4-0',         // Anthropic
  'gemini-2.5-pro',            // Google
  'deepseek-r1-distill-llama-70b', // Groq
  'llama-3.3-70b-versatile'    // Groq
];

// Model to Provider Mapping (ensures correct provider is used for each model)
export const MODEL_PROVIDER_MAP: Record<string, string> = {
  // OpenAI Models
  'gpt-4o': 'openai',
  'gpt-4.1-2025-04-14': 'openai',
  'o3-2025-04-16': 'openai',
  'o3-mini-2025-01-31': 'openai',
  'o1-mini-2024-09-12': 'openai',

  // Anthropic Models
  'claude-sonnet-4-0': 'anthropic',
  'claude-opus-4-0': 'anthropic',
  'claude-sonnet-4-20250514': 'anthropic',

  // Google Models
  'gemini-2.5-pro': 'google',
  'gemini-1.5-pro': 'google',
  'gemini-1.5-flash': 'google',
  'gemini-2.5-flash': 'google',

  // Groq Models
  'deepseek-r1-distill-llama-70b': 'groq',
  'llama-3.3-70b-versatile': 'groq',
  'meta-llama/llama-4-maverick-17b-128e-instruct': 'groq'
};

// System Capabilities
export const SYSTEM_CAPABILITIES = {
  MAX_JOURNALISTS: 6,
  MIN_JOURNALISTS: 1,
  SUPPORTED_PROVIDERS: ['openai', 'anthropic', 'google', 'groq'],
  SUPPORTED_INVESTIGATION_TYPES: Object.values(InvestigationType),
  FEATURES: [
    'Multi-LLM Comparison',
    'Specialized Journalist Personas',
    'Cross-Verification Analysis',
    'Professional Consolidation',
    'PMO Integration',
    'Team Collaboration',
    'Progress Tracking',
    'Quality Assessment'
  ]
};

// Utility Functions

/**
 * Get the correct provider for a given model
 * @param model - The model name
 * @returns The provider name or 'openai' as default
 */
export function getProviderForModel(model: string): string {
  return MODEL_PROVIDER_MAP[model] || 'openai';
}

/**
 * Validate if a model-provider combination is valid
 * @param model - The model name
 * @param provider - The provider name
 * @returns True if the combination is valid
 */
export function isValidModelProviderCombination(model: string, provider: string): boolean {
  const correctProvider = MODEL_PROVIDER_MAP[model];
  return correctProvider === provider;
}

export function getInvestigationTypeFromString(type: string): InvestigationType | null {
  const normalizedType = type.toLowerCase().replace(/[^a-z]/g, '_');
  
  switch (normalizedType) {
    case 'financial':
    case 'finance':
      return InvestigationType.FINANCIAL;
    case 'political':
    case 'politics':
      return InvestigationType.POLITICAL;
    case 'technology':
    case 'tech':
      return InvestigationType.TECHNOLOGY;
    case 'social_affairs':
    case 'social':
      return InvestigationType.SOCIAL_AFFAIRS;
    case 'corporate':
    case 'business':
      return InvestigationType.CORPORATE;
    case 'environmental':
    case 'environment':
      return InvestigationType.ENVIRONMENTAL;
    case 'feature':
    case 'narrative':
      return InvestigationType.FEATURE;
    case 'investigative':
    case 'general':
    default:
      return InvestigationType.INVESTIGATIVE;
  }
}

export function getRecommendedJournalistCount(investigationType: InvestigationType): number {
  switch (investigationType) {
    case InvestigationType.FEATURE:
      return 4; // Feature stories benefit from multiple perspectives
    case InvestigationType.FINANCIAL:
    case InvestigationType.POLITICAL:
      return 3; // Complex domains need specialized analysis
    case InvestigationType.TECHNOLOGY:
    case InvestigationType.CORPORATE:
    case InvestigationType.ENVIRONMENTAL:
      return 3; // Moderate complexity
    case InvestigationType.SOCIAL_AFFAIRS:
      return 2; // Often more focused
    case InvestigationType.INVESTIGATIVE:
    default:
      return 3; // General investigations benefit from multiple angles
  }
}

export function calculateInvestigationComplexity(
  investigationType: InvestigationType,
  description: string,
  journalistCount: number = 3
): number {
  let score = 0;
  
  // Base complexity by type
  switch (investigationType) {
    case InvestigationType.FINANCIAL:
    case InvestigationType.POLITICAL:
      score += 15;
      break;
    case InvestigationType.TECHNOLOGY:
    case InvestigationType.CORPORATE:
      score += 10;
      break;
    case InvestigationType.FEATURE:
      score += 25;
      break;
    default:
      score += 5;
  }
  
  // Description complexity
  const descriptionLength = description.length;
  if (descriptionLength > 500) score += 10;
  else if (descriptionLength > 200) score += 5;
  
  // Journalist count factor
  score += journalistCount * 3;
  
  // Keywords that increase complexity
  const complexKeywords = ['multi-source', 'cross-verification', 'comprehensive', 'detailed', 'thorough'];
  const keywordMatches = complexKeywords.filter(keyword => 
    description.toLowerCase().includes(keyword)
  ).length;
  score += keywordMatches * 5;
  
  return Math.min(score, 100); // Cap at 100
}
